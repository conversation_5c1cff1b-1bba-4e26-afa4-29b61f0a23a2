import { <PERSON><PERSON>, type CommandContext, Context, InlineKeyboard } from "grammy"
import { log } from "./log"
import { Content } from "./content"
import { User, Wallet, TradingHistory, Fees } from "./models"
import { getChainType, getChainId, generateMockAddress, format<PERSON>hainName, getCurrencySymbol, generateMockSeed } from "./tools/shared"
import { dataChainList, dataChainName, type Type<PERSON>hainName } from "./data"

const content = new Content()

// Interface for keyboard state management
interface KeyboardState {
  messageId: number
  chatId: number
  type: string
  data?: any
}

// Store active keyboards for state management
const activeKeyboards = new Map<string, KeyboardState>()

/**
 * Telegram bot class for the trading bot with inline keyboard support
 *
 * This class handles all Telegram bot interactions, including user commands
 * for wallet management, trading operations, and system information.
 * It integrates with the PostgreSQL database through the model layer and
 * provides dynamic inline keyboards with proper state management.
 */
export class Telegram {
  public bot = new Bot(process.env.TELEGRAM_TOKEN || ``)

  /**
   * Reusable helper method to reply with content
   * @param ctx Telegram context object
   * @param contentKey Content key to retrieve from content system
   * @param data Optional data object for variable replacement
   * @param options Optional reply options
   * @returns Promise from ctx.reply()
   */
  private async reply(ctx: CommandContext<Context>, contentKey: string, data?: Record<string, any>, options?: any): Promise<any> {
    return ctx.reply(content.get(contentKey, data), { parse_mode: `MarkdownV2`, ...options })
  }

  /**
   * Send message with inline keyboard and track state
   * @param ctx Telegram context object
   * @param contentKey Content key to retrieve from content system
   * @param keyboard Inline keyboard markup
   * @param keyboardType Type identifier for the keyboard
   * @param data Optional data object for variable replacement
   * @param keyboardData Optional data to store with keyboard state
   * @returns Promise from ctx.reply()
   */
  private async replyWithKeyboard(ctx: CommandContext<Context>, contentKey: string, keyboard: InlineKeyboard, keyboardType: string, data?: Record<string, any>, keyboardData?: any): Promise<any> {
    try {
      const message = await ctx.reply(content.get(contentKey, data), {
        parse_mode: `MarkdownV2`,
        reply_markup: keyboard
      })

      // Store keyboard state for management
      const stateKey = `${ctx.chat?.id}_${message.message_id}`
      activeKeyboards.set(stateKey, {
        messageId: message.message_id,
        chatId: ctx.chat?.id || 0,
        type: keyboardType,
        data: keyboardData
      })

      return message
    } catch (error) {
      log.error(`Error sending message with keyboard: ${error}`)
      return this.reply(ctx, contentKey, data)
    }
  }

  /**
   * Update existing inline keyboard or delete if update fails
   * @param ctx Callback query context
   * @param contentKey Content key for new message text
   * @param keyboard New inline keyboard markup
   * @param data Optional data object for variable replacement
   * @returns Promise indicating success
   */
  private async updateKeyboard(ctx: any, contentKey: string, keyboard: InlineKeyboard, data?: Record<string, any>): Promise<boolean> {
    try {
      await ctx.editMessageText(content.get(contentKey, data), {
        parse_mode: `MarkdownV2`,
        reply_markup: keyboard
      })
      return true
    } catch (error) {
      log.error(`Failed to update keyboard, attempting deletion: ${error}`)
      try {
        await ctx.deleteMessage()
        // Remove from active keyboards
        const stateKey = `${ctx.chat?.id}_${ctx.msg?.message_id}`
        activeKeyboards.delete(stateKey)
      } catch (deleteError) {
        log.error(`Failed to delete message: ${deleteError}`)
      }
      return false
    }
  }

  /**
   * Delete inline keyboard message
   * @param ctx Callback query context
   * @returns Promise indicating success
   */
  private async deleteKeyboard(ctx: any): Promise<boolean> {
    try {
      await ctx.deleteMessage()
      const stateKey = `${ctx.chat?.id}_${ctx.msg?.message_id}`
      activeKeyboards.delete(stateKey)
      return true
    } catch (error) {
      log.error(`Failed to delete keyboard: ${error}`)
      return false
    }
  }

  /**
   * Answer callback query with content from .md file
   * @param ctx Callback query context
   * @param contentKey Content key to retrieve from content system
   * @param data Optional data object for variable replacement
   * @returns Promise from ctx.answerCallbackQuery()
   */
  private async answerCallback(ctx: any, contentKey: string, data?: Record<string, any>): Promise<void> {
    try {
      await ctx.answerCallbackQuery(content.get(contentKey, data))
    } catch (error) {
      log.error(`Failed to answer callback query: ${error}`)
      // Fallback to empty answer to prevent timeout
      try {
        await ctx.answerCallbackQuery()
      } catch (fallbackError) {
        log.error(`Failed to answer callback query with fallback: ${fallbackError}`)
      }
    }
  }

  constructor() {
    // Register basic commands
    this.bot.command(`start`, this.handleCommandStart.bind(this))
    this.bot.command(`help`, this.handleCommandHelp.bind(this))

    // Wallet management commands with inline keyboards
    this.bot.command(`createwallet`, this.handleCommandCreateWallet.bind(this))
    this.bot.command(`createwallets`, this.handleCommandCreateMultipleWallets.bind(this))
    this.bot.command(`wallets`, this.handleCommandListWallets.bind(this))
    this.bot.command(`wallet`, this.handleCommandWalletInfo.bind(this))
    this.bot.command(`importwallet`, this.handleCommandImportWallet.bind(this))
    this.bot.command(`deletewallet`, this.handleCommandDeleteWallet.bind(this))
    this.bot.command(`balance`, this.handleCommandBalance.bind(this))

    // Trading and history commands
    this.bot.command(`history`, this.handleCommandTradingHistory.bind(this))
    this.bot.command(`stats`, this.handleCommandStats.bind(this))

    // Fee and configuration commands
    this.bot.command(`fees`, this.handleCommandFees.bind(this))
    this.bot.command(`chains`, this.handleCommandChains.bind(this))

    // Register callback query handlers for inline keyboards
    this.bot.on("callback_query:data", this.handleCallbackQuery.bind(this))

    // Handled error
    this.bot.catch((err) => {
      log.error(JSON.stringify(err, null, 2))
    })

    // Started bot
    this.bot.start({
      onStart(botInfo) {
        log.info(`Telegram bot started: ${botInfo.username}`)
      }
    })
  }

  /**
   * Handle callback queries from inline keyboards
   * @param ctx Callback query context
   */
  private async handleCallbackQuery(ctx: any): Promise<void> {
    try {
      await ctx.answerCallbackQuery()

      const data = ctx.callbackQuery.data
      const [action, ...params] = data.split(":")

      switch (action) {
        case "wallet_action":
          await this.handleWalletAction(ctx, params)
          break
        case "wallet_select":
          await this.handleWalletSelect(ctx, params)
          break
        case "wallet_detail":
          await this.handleWalletDetail(ctx, params)
          break
        case "wallet_delete_select":
          await this.handleWalletDeleteSelect(ctx, params)
          break
        case "chain_select":
          await this.handleChainSelect(ctx, params)
          break
        case "chain_detail":
          await this.handleChainDetail(ctx, params)
          break
        case "confirm_delete":
          await this.handleConfirmDelete(ctx, params)
          break
        case "create_wallet":
          await this.handleCreateWalletChain(ctx, params)
          break
        case "import_manual":
          await this.handleImportManual(ctx)
          break
        case "import_seed":
          await this.handleImportSeed(ctx)
          break
        case "help":
          await this.handleHelpSection(ctx, params)
          break
        case "settings":
          await this.handleSettingsAction(ctx, params)
          break
        case "stats_detailed":
          await this.handleDetailedStats(ctx)
          break
        case "cancel":
          await this.handleCancel(ctx)
          break
        case "back":
          await this.handleBack(ctx, params)
          break
        case "page":
          await this.handlePagination(ctx, params)
          break
        default:
          log.warn(`Unknown callback action: ${action}`)
          await this.deleteKeyboard(ctx)
      }
    } catch (error) {
      log.error(`Error handling callback query: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle the /start command with inline keyboard
   * @param ctx Command context from Grammy
   */
  public async handleCommandStart(ctx: CommandContext<Context>): Promise<void> {
    const keyboard = new InlineKeyboard()
      .text("💼 My Wallets", "wallet_action:list")
      .text("➕ Create Wallet", "wallet_action:create")
      .row()
      .text("📥 Import Wallet", "wallet_action:import")
      .text("📊 View Stats", "wallet_action:stats")
      .row()
      .text("⚙️ Settings", "wallet_action:settings")
      .text("❓ Help", "wallet_action:help")

    await this.replyWithKeyboard(ctx, "start", keyboard, "main_menu")
  }

  /**
   * Handle the /help command with inline keyboard
   * @param ctx Command context from Grammy
   */
  public async handleCommandHelp(ctx: CommandContext<Context>): Promise<void> {
    const keyboard = new InlineKeyboard().text("💼 Wallet Commands", "help:wallets").text("📊 Trading Commands", "help:trading").row().text("⚙️ Config Commands", "help:config").text("🔙 Back to Menu", "back:main_menu")

    await this.replyWithKeyboard(ctx, "help", keyboard, "help_menu")
  }

  /**
   * Handle the /createwallet command
   * Format: /createwallet <name>
   */
  public async handleCommandCreateWallet(ctx: CommandContext<Context>): Promise<void> {
    try {
      // Get command arguments
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 1) {
        await this.reply(ctx, "createwallet_usage")
        return
      }

      const walletName = args[0] as string

      // Get or create user
      const telegramId = ctx.from?.id || 0
      const user = await User.getOrCreate(telegramId, ctx.from?.username || `user_${telegramId}`, ctx.from?.first_name || "Unknown")

      if (!user) {
        await this.reply(ctx, "createwallet_failed_user")
        return
      }

      // Use default chain if not provided (for backward compatibility)
      const chain = `solana_mainnet`
      const address = generateMockAddress(chain)
      const chainId = getChainId(chain)
      const seed = generateMockSeed()

      // Create wallet
      const wallet = await Wallet.create(user.id, walletName, chain, chainId, address, seed, "system")

      if (!wallet) {
        await this.reply(ctx, "createwallet_failed", { walletName })
        return
      }

      await this.reply(ctx, "createwallet_success_new", {
        walletName,
        chain: formatChainName(chain),
        address
      })
    } catch (error) {
      log.error(`Error in handleCommandCreateWallet: ${error}`)
      await this.reply(ctx, "createwallet_error")
    }
  }

  /**
   * Handle the /wallets command to list all wallets with inline keyboard
   */
  public async handleCommandListWallets(ctx: CommandContext<Context>): Promise<void> {
    try {
      // Get user
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.reply(ctx, "wallets_no_user")
        return
      }

      // Get all wallets for user
      const wallets = await Wallet.getAllForUser(user.id)

      if (wallets.length === 0) {
        const keyboard = new InlineKeyboard().text("➕ Create Wallet", "wallet_action:create").text("📥 Import Wallet", "wallet_action:import").row().text("🔙 Back to Menu", "back:main_menu")

        await this.replyWithKeyboard(ctx, "wallets_empty", keyboard, "empty_wallets")
        return
      }

      // Create inline keyboard for wallet selection
      const keyboard = new InlineKeyboard()

      // Add wallet buttons (max 5 per page for better UX)
      const walletsPerPage = 5
      const totalPages = Math.ceil(wallets.length / walletsPerPage)
      const currentPage = 0 // Default to first page
      const startIndex = currentPage * walletsPerPage
      const endIndex = Math.min(startIndex + walletsPerPage, wallets.length)

      for (let i = startIndex; i < endIndex; i++) {
        const wallet = wallets[i] as any
        const balance = wallet.balance.toString()
        const symbol = getCurrencySymbol(wallet.chain as TypeChainName)
        keyboard.text(`💼 ${wallet.name} (${balance} ${symbol})`, `wallet_select:${wallet.id}`).row()
      }

      // Add pagination if needed
      if (totalPages > 1) {
        const paginationRow = new InlineKeyboard()
        if (currentPage > 0) {
          paginationRow.text("⬅️ Previous", `page:wallets:${currentPage - 1}`)
        }
        paginationRow.text(`${currentPage + 1}/${totalPages}`, "page:info")
        if (currentPage < totalPages - 1) {
          paginationRow.text("➡️ Next", `page:wallets:${currentPage + 1}`)
        }
        keyboard.row().add(paginationRow)
      }

      // Add action buttons
      keyboard.row().text("➕ Create", "wallet_action:create").text("📥 Import", "wallet_action:import").text("🔙 Menu", "back:main_menu")

      // Format wallet list for display
      const walletList = wallets
        .slice(startIndex, endIndex)
        .map((wallet, index) => {
          const balance = wallet.balance.toString()
          const symbol = getCurrencySymbol(wallet.chain as TypeChainName)
          const chainName = formatChainName(wallet.chain as TypeChainName)
          return `${startIndex + index + 1}. ${wallet.name}\n   🔗 ${chainName}\n   💰 ${balance} ${symbol}\n   📍 ${wallet.address}`
        })
        .join("\n\n")

      await this.replyWithKeyboard(ctx, "wallets_list", keyboard, "wallet_list", {
        walletList,
        totalWallets: wallets.length,
        currentPage: currentPage + 1,
        totalPages
      })
    } catch (error) {
      log.error(`Error in handleCommandListWallets: ${error}`)
      await this.reply(ctx, "wallets_error")
    }
  }

  /**
   * Handle the /importwallet command
   * Format: /importwallet <name> <seed>
   */
  public async handleCommandImportWallet(ctx: CommandContext<Context>): Promise<void> {
    try {
      // Get command arguments
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 2) {
        await this.reply(ctx, "importwallet_usage")
        return
      }

      const walletName = args[0] as string
      const seed = args[1] as string

      // Get or create user
      const telegramId = ctx.from?.id || 0
      const user = await User.getOrCreate(telegramId, ctx.from?.username || `user_${telegramId}`, ctx.from?.first_name || "Unknown")

      if (!user) {
        await this.reply(ctx, "createwallet_failed_user")
        return
      }

      // Use default chain for backward compatibility
      const chain = `solana_mainnet`
      const address = generateMockAddress(chain)
      const chainId = getChainId(chain)

      // Import wallet
      const wallet = await Wallet.import(user.id, walletName, chain, chainId, address, seed)

      if (!wallet) {
        await this.reply(ctx, "importwallet_failed", { walletName })
        return
      }

      await this.reply(ctx, "importwallet_success_new", {
        walletName,
        chain: formatChainName(chain),
        address
      })
    } catch (error) {
      log.error(`Error in handleCommandImportWallet: ${error}`)
      await this.reply(ctx, "importwallet_error")
    }
  }

  /**
   * Handle the /deletewallet command with confirmation dialog
   * Format: /deletewallet <name>
   */
  public async handleCommandDeleteWallet(ctx: CommandContext<Context>): Promise<void> {
    try {
      // Get command arguments
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 1) {
        // Show wallet selection for deletion
        await this.showDeleteWalletSelection(ctx)
        return
      }

      const walletName = args[0] as string

      // Get user
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.reply(ctx, "deletewallet_no_wallets")
        return
      }

      // Find wallet by name
      const wallet = await Wallet.getByName(user.id, walletName)

      if (!wallet) {
        await this.reply(ctx, "deletewallet_not_found", { walletName })
        return
      }

      // Show confirmation dialog with inline keyboard
      const keyboard = new InlineKeyboard().text("✅ Yes, Delete", `confirm_delete:${wallet.id}`).text("❌ Cancel", "cancel").row().text("🔙 Back to Wallets", "wallet_action:list")

      await this.replyWithKeyboard(ctx, "deletewallet_confirm", keyboard, "delete_confirmation", {
        walletName: wallet.name,
        chainName: formatChainName(wallet.chain as TypeChainName),
        address: wallet.address
      })
    } catch (error) {
      log.error(`Error in handleCommandDeleteWallet: ${error}`)
      await this.reply(ctx, "deletewallet_error")
    }
  }

  /**
   * Show wallet selection for deletion
   * @param ctx Command context
   */
  private async showDeleteWalletSelection(ctx: CommandContext<Context>): Promise<void> {
    try {
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.reply(ctx, "deletewallet_no_wallets")
        return
      }

      const wallets = await Wallet.getAllForUser(user.id)

      if (wallets.length === 0) {
        await this.reply(ctx, "deletewallet_no_wallets")
        return
      }

      const keyboard = new InlineKeyboard()

      // Add wallet buttons for deletion
      wallets.forEach((wallet) => {
        const balance = wallet.balance.toString()
        const symbol = getCurrencySymbol(wallet.chain as TypeChainName)
        keyboard.text(`🗑️ ${wallet.name} (${balance} ${symbol})`, `wallet_delete_select:${wallet.id}`).row()
      })

      keyboard.row().text("❌ Cancel", "cancel")

      await this.replyWithKeyboard(ctx, "deletewallet_usage", keyboard, "delete_selection")
    } catch (error) {
      log.error(`Error in showDeleteWalletSelection: ${error}`)
      await this.reply(ctx, "deletewallet_error")
    }
  }

  /**
   * Handle the /createwallets command to create multiple wallets at once
   * Format: /createwallets <count> <prefix>
   */
  public async handleCommandCreateMultipleWallets(ctx: CommandContext<Context>): Promise<void> {
    try {
      // Get command arguments
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 2) {
        await this.reply(ctx, "createwallets_usage")
        return
      }

      const count = parseInt(args[0] as string, 10)
      const prefix = args[1] as string

      if (isNaN(count) || count <= 0 || count > 100) {
        await this.reply(ctx, "createwallets_count_invalid")
        return
      }

      // Get or create user
      const telegramId = ctx.from?.id || 0
      const user = await User.getOrCreate(telegramId, ctx.from?.username || `user_${telegramId}`, ctx.from?.first_name || "Unknown")

      if (!user) {
        await this.reply(ctx, "createwallet_failed_user")
        return
      }

      // Create wallets
      const createdWallets = []
      const failedWallets = []

      // Use default chain for batch creation
      const chain = `solana_mainnet`
      const chainId = getChainId(chain)

      for (let i = 1; i <= count; i++) {
        const walletName = `${prefix}${i}`
        const address = generateMockAddress(chain)
        const seed = generateMockSeed()

        const wallet = await Wallet.create(user.id, walletName, chain, chainId, address, seed, "system")

        if (wallet) {
          createdWallets.push(walletName)
        } else {
          failedWallets.push(walletName)
        }
      }

      // Build result message using content system
      const successMessage =
        createdWallets.length > 0
          ? content.get("createwallets_success", {
              count: createdWallets.length,
              walletList: createdWallets.join(", ")
            })
          : ""

      const failureMessage =
        failedWallets.length > 0
          ? content.get("createwallets_failed", {
              count: failedWallets.length,
              walletList: failedWallets.join(", ")
            })
          : ""

      const message = [successMessage, failureMessage].filter((msg) => msg).join("\n\n")

      await this.reply(ctx, "createwallets_result", { message })
    } catch (error) {
      log.error(`Error in handleCommandCreateMultipleWallets: ${error}`)
      await this.reply(ctx, "createwallets_error")
    }
  }

  /**
   * Handle the /wallet command to show detailed wallet information
   * Format: /wallet <name>
   * @param ctx Command context from Grammy
   */
  public async handleCommandWalletInfo(ctx: CommandContext<Context>): Promise<void> {
    try {
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 1) {
        await this.reply(ctx, "wallet_usage")
        return
      }

      const walletName = args[0] as string
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.reply(ctx, "user_not_found")
        return
      }

      const wallet = await Wallet.getByName(user.id, walletName)

      if (!wallet) {
        await this.reply(ctx, "wallet_not_found", { walletName })
        return
      }

      const chainName = formatChainName(wallet.chain as TypeChainName)
      const symbol = getCurrencySymbol(wallet.chain as TypeChainName)
      const balance = wallet.balance.toString()
      const createdDate = wallet.createdAt?.toLocaleDateString() || "Unknown"

      await this.reply(ctx, "wallet_info", {
        name: wallet.name,
        chainName,
        address: wallet.address,
        balance,
        symbol,
        chainId: wallet.chainId,
        createdDate,
        createdBy: wallet.createdBy
      })
    } catch (error) {
      log.error(`Error in handleCommandWalletInfo: ${error}`)
      await this.reply(ctx, "wallet_info_error")
    }
  }

  /**
   * Handle the /balance command to show wallet balance
   * Format: /balance <name>
   * @param ctx Command context from Grammy
   */
  public async handleCommandBalance(ctx: CommandContext<Context>): Promise<void> {
    try {
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 1) {
        await this.reply(ctx, "balance_usage")
        return
      }

      const walletName = args[0] as string
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.reply(ctx, "user_not_found")
        return
      }

      const wallet = await Wallet.getByName(user.id, walletName)

      if (!wallet) {
        await this.reply(ctx, "wallet_not_found", { walletName })
        return
      }

      const symbol = getCurrencySymbol(wallet.chain as TypeChainName)
      const balance = wallet.balance.toString()
      const chainName = formatChainName(wallet.chain as TypeChainName)

      await this.reply(ctx, "balance_display", {
        walletName: wallet.name,
        balance,
        symbol,
        chainName
      })
    } catch (error) {
      log.error(`Error in handleCommandBalance: ${error}`)
      await this.reply(ctx, "balance_error")
    }
  }

  /**
   * Handle the /history command to show trading history
   * Format: /history <wallet_name>
   * @param ctx Command context from Grammy
   */
  public async handleCommandTradingHistory(ctx: CommandContext<Context>): Promise<void> {
    try {
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 1) {
        await this.reply(ctx, "history_usage")
        return
      }

      const walletName = args[0] as string
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.reply(ctx, "user_not_found")
        return
      }

      const wallet = await Wallet.getByName(user.id, walletName)

      if (!wallet) {
        await this.reply(ctx, "wallet_not_found", { walletName })
        return
      }

      const history = await TradingHistory.getForWallet(BigInt(wallet.id), 10)

      if (history.length === 0) {
        await this.reply(ctx, "history_empty", { walletName })
        return
      }

      const historyText = history
        .map((trade, index) => {
          const operation = trade.operation === "B" ? "🟢 BUY" : "🔴 SELL"
          const status = trade.success ? "✅" : "❌"
          const amount = trade.amount.toString()
          const symbol = getCurrencySymbol(trade.chain as TypeChainName)
          const date = trade.createdAt?.toLocaleDateString() || "Unknown"

          return `${index + 1}. ${operation} ${status}\n   💰 ${amount} ${symbol}\n   📅 ${date}`
        })
        .join("\n\n")

      await this.reply(ctx, "history_display", { walletName, historyText })
    } catch (error) {
      log.error(`Error in handleCommandTradingHistory: ${error}`)
      await this.reply(ctx, "history_error")
    }
  }

  /**
   * Handle the /stats command to show trading statistics
   * Format: /stats <wallet_name>
   * @param ctx Command context from Grammy
   */
  public async handleCommandStats(ctx: CommandContext<Context>): Promise<void> {
    try {
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 1) {
        await this.reply(ctx, "stats_usage")
        return
      }

      const walletName = args[0] as string
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.reply(ctx, "user_not_found")
        return
      }

      const wallet = await Wallet.getByName(user.id, walletName)

      if (!wallet) {
        await this.reply(ctx, "wallet_not_found", { walletName })
        return
      }

      const stats = await TradingHistory.getWalletStats(BigInt(wallet.id))

      if (!stats) {
        await this.reply(ctx, "stats_empty", { walletName })
        return
      }

      const successRate = stats.totalTrades > 0 ? ((stats.successTrades / stats.totalTrades) * 100).toFixed(2) : "0"
      const symbol = getCurrencySymbol(wallet.chain as TypeChainName)

      await this.reply(ctx, "stats_display", {
        walletName,
        totalTrades: stats.totalTrades,
        successTrades: stats.successTrades,
        failedTrades: stats.failedTrades,
        successRate,
        totalVolume: stats.totalVolume.toString(),
        successVolume: stats.successVolume.toString(),
        symbol,
        buyTrades: stats.buyTrades,
        sellTrades: stats.sellTrades
      })
    } catch (error) {
      log.error(`Error in handleCommandStats: ${error}`)
      await this.reply(ctx, "stats_error")
    }
  }

  /**
   * Handle the /fees command to show fee configuration
   * Format: /fees <chain>
   * @param ctx Command context from Grammy
   */
  public async handleCommandFees(ctx: CommandContext<Context>): Promise<void> {
    try {
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 1) {
        await this.reply(ctx, "fees_usage")
        return
      }

      const chainInput = args[0] as string

      if (chainInput.toLowerCase() === "all") {
        const allFees = await Fees.getAll()

        if (allFees.length === 0) {
          await this.reply(ctx, "fees_empty")
          return
        }

        const feesList = allFees
          .map((fee, index) => {
            const chainName = formatChainName(fee.chain as TypeChainName)
            return `${index + 1}. ${chainName}\n   💰 Fee: ${fee.fee}%\n   📍 Receiver: \`${fee.receiver}\``
          })
          .join("\n\n")

        await this.reply(ctx, "fees_all", { feesList })
        return
      }

      const chain = getChainType(chainInput)
      if (!chain) {
        await this.reply(ctx, "chain_invalid")
        return
      }

      const fees = await Fees.getByChain(chain)

      if (fees.length === 0) {
        await this.reply(ctx, "fees_chain_empty", { chainName: formatChainName(chain) })
        return
      }

      const chainName = formatChainName(chain)
      const feesList = fees
        .map((fee, index) => {
          return `${index + 1}. Chain ID: ${fee.chainId}\n   💰 Fee: ${fee.fee}%\n   📍 Receiver: \`${fee.receiver}\``
        })
        .join("\n\n")

      await this.reply(ctx, "fees_chain_display", { chainName, feesList })
    } catch (error) {
      log.error(`Error in handleCommandFees: ${error}`)
      await this.reply(ctx, "fees_error")
    }
  }

  /**
   * Handle the /chains command to show supported blockchain networks
   * @param ctx Command context from Grammy
   */
  public async handleCommandChains(ctx: CommandContext<Context>): Promise<void> {
    try {
      const chainsList = dataChainName
        .map((chain, index) => {
          const chainName = formatChainName(chain)
          const symbol = getCurrencySymbol(chain)
          const chainId = getChainId(chain)
          return `${index + 1}. ${chainName}\n   🔗 ID: ${chainId}\n   💰 Symbol: ${symbol}\n   📝 Code: \`${chain}\``
        })
        .join("\n\n")

      await this.reply(ctx, "chains_display", { chainsList })
    } catch (error) {
      log.error(`Error in handleCommandChains: ${error}`)
      await this.reply(ctx, "chains_error")
    }
  }

  /**
   * Handle wallet action callbacks
   * @param ctx Callback query context
   * @param params Action parameters
   */
  private async handleWalletAction(ctx: any, params: string[]): Promise<void> {
    const action = params[0]

    switch (action) {
      case "list":
        await this.showWalletList(ctx)
        break
      case "create":
        await this.showCreateWalletForm(ctx)
        break
      case "import":
        await this.showImportWalletForm(ctx)
        break
      case "stats":
        await this.showUserStats(ctx)
        break
      case "settings":
        await this.showSettings(ctx)
        break
      case "help":
        await this.showHelpMenu(ctx)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle wallet selection callbacks
   * @param ctx Callback query context
   * @param params Selection parameters
   */
  private async handleWalletSelect(ctx: any, params: string[]): Promise<void> {
    const walletId = parseInt(params[0])
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    const wallet = await Wallet.getById(walletId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_not_found")
      await this.deleteKeyboard(ctx)
      return
    }

    // Show wallet details with action buttons
    const keyboard = new InlineKeyboard()
      .text("💰 Balance", `wallet_detail:balance:${walletId}`)
      .text("📊 History", `wallet_detail:history:${walletId}`)
      .row()
      .text("📈 Stats", `wallet_detail:stats:${walletId}`)
      .text("🗑️ Delete", `wallet_detail:delete:${walletId}`)
      .row()
      .text("🔙 Back to Wallets", "wallet_action:list")

    const chainName = formatChainName(wallet.chain as TypeChainName)
    const symbol = getCurrencySymbol(wallet.chain as TypeChainName)
    const balance = wallet.balance.toString()
    const createdDate = wallet.createdAt?.toLocaleDateString() || "Unknown"

    await this.updateKeyboard(ctx, "wallet_info", keyboard, {
      name: wallet.name,
      chainName,
      address: wallet.address,
      balance,
      symbol,
      chainId: wallet.chainId,
      createdDate,
      createdBy: wallet.createdBy
    })
  }

  /**
   * Handle chain selection callbacks
   * @param ctx Callback query context
   * @param params Chain parameters
   */
  private async handleChainSelect(ctx: any, params: string[]): Promise<void> {
    const chain = params[0] as TypeChainName
    const action = params[1] || "info"

    if (action === "info") {
      const chainName = formatChainName(chain)
      const symbol = getCurrencySymbol(chain)
      const chainId = getChainId(chain)

      const keyboard = new InlineKeyboard().text("💰 View Fees", `chain_detail:fees:${chain}`).text("📊 Network Stats", `chain_detail:stats:${chain}`).row().text("🔙 Back to Chains", "wallet_action:chains")

      await this.updateKeyboard(ctx, "chain_info", keyboard, {
        chainName,
        symbol,
        chainId,
        chain
      })
    }
  }

  /**
   * Handle delete confirmation callbacks
   * @param ctx Callback query context
   * @param params Delete parameters
   */
  private async handleConfirmDelete(ctx: any, params: string[]): Promise<void> {
    const walletId = parseInt(params[0])
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    const telegramId = ctx.from?.id || 0
    const user = await User.getById(telegramId)

    if (!user) {
      await this.answerCallback(ctx, "callback_user_not_found")
      await this.deleteKeyboard(ctx)
      return
    }

    const wallet = await Wallet.getById(walletId)
    if (!wallet || wallet.owner !== user.id) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }

    const success = await Wallet.delete(walletId, user.id)

    if (success) {
      await this.answerCallback(ctx, "callback_wallet_deleted_success")
      await this.showWalletList(ctx)
    } else {
      await this.answerCallback(ctx, "callback_wallet_delete_failed")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle cancel callbacks
   * @param ctx Callback query context
   */
  private async handleCancel(ctx: any): Promise<void> {
    await this.answerCallback(ctx, "callback_cancelled")
    await this.deleteKeyboard(ctx)
  }

  /**
   * Handle back navigation callbacks
   * @param ctx Callback query context
   * @param params Navigation parameters
   */
  private async handleBack(ctx: any, params: string[]): Promise<void> {
    const destination = params[0]

    switch (destination) {
      case "main_menu":
        await this.showMainMenu(ctx)
        break
      case "wallets":
        await this.showWalletList(ctx)
        break
      case "help":
        await this.showHelpMenu(ctx)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle pagination callbacks
   * @param ctx Callback query context
   * @param params Pagination parameters
   */
  private async handlePagination(ctx: any, params: string[]): Promise<void> {
    const type = params[0]
    const page = parseInt(params[1])

    if (isNaN(page)) {
      await this.deleteKeyboard(ctx)
      return
    }

    switch (type) {
      case "wallets":
        await this.showWalletListPage(ctx, page)
        break
      case "info":
        // Just acknowledge, don't change anything
        await ctx.answerCallbackQuery()
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show main menu with inline keyboard
   * @param ctx Callback query context
   */
  private async showMainMenu(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard()
      .text("💼 My Wallets", "wallet_action:list")
      .text("➕ Create Wallet", "wallet_action:create")
      .row()
      .text("📥 Import Wallet", "wallet_action:import")
      .text("📊 View Stats", "wallet_action:stats")
      .row()
      .text("⚙️ Settings", "wallet_action:settings")
      .text("❓ Help", "wallet_action:help")

    await this.updateKeyboard(ctx, "start", keyboard)
  }

  /**
   * Show wallet list with inline keyboard
   * @param ctx Callback query context
   */
  private async showWalletList(ctx: any): Promise<void> {
    await this.showWalletListPage(ctx, 0)
  }

  /**
   * Show wallet list page with pagination
   * @param ctx Callback query context
   * @param page Page number (0-based)
   */
  private async showWalletListPage(ctx: any, page: number): Promise<void> {
    try {
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.answerCallback(ctx, "callback_user_not_found")
        await this.deleteKeyboard(ctx)
        return
      }

      const wallets = await Wallet.getAllForUser(user.id)

      if (wallets.length === 0) {
        const keyboard = new InlineKeyboard().text("➕ Create Wallet", "wallet_action:create").text("📥 Import Wallet", "wallet_action:import").row().text("🔙 Back to Menu", "back:main_menu")

        await this.updateKeyboard(ctx, "wallets_empty", keyboard)
        return
      }

      // Create inline keyboard for wallet selection
      const keyboard = new InlineKeyboard()

      // Add wallet buttons (max 5 per page for better UX)
      const walletsPerPage = 5
      const totalPages = Math.ceil(wallets.length / walletsPerPage)
      const currentPage = Math.max(0, Math.min(page, totalPages - 1))
      const startIndex = currentPage * walletsPerPage
      const endIndex = Math.min(startIndex + walletsPerPage, wallets.length)

      for (let i = startIndex; i < endIndex; i++) {
        const wallet = wallets[i] as any
        const balance = wallet.balance.toString()
        const symbol = getCurrencySymbol(wallet.chain as TypeChainName)
        keyboard.text(`💼 ${wallet.name} (${balance} ${symbol})`, `wallet_select:${wallet.id}`).row()
      }

      // Add pagination if needed
      if (totalPages > 1) {
        const paginationRow = new InlineKeyboard()
        if (currentPage > 0) {
          paginationRow.text("⬅️ Previous", `page:wallets:${currentPage - 1}`)
        }
        paginationRow.text(`${currentPage + 1}/${totalPages}`, "page:info")
        if (currentPage < totalPages - 1) {
          paginationRow.text("➡️ Next", `page:wallets:${currentPage + 1}`)
        }
        keyboard.row().add(paginationRow)
      }

      // Add action buttons
      keyboard.row().text("➕ Create", "wallet_action:create").text("📥 Import", "wallet_action:import").text("🔙 Menu", "back:main_menu")

      // Format wallet list for display
      const walletList = wallets
        .slice(startIndex, endIndex)
        .map((wallet, index) => {
          const balance = wallet.balance.toString()
          const symbol = getCurrencySymbol(wallet.chain as TypeChainName)
          const chainName = formatChainName(wallet.chain as TypeChainName)
          return `${startIndex + index + 1}. ${wallet.name}\n   🔗 ${chainName}\n   💰 ${balance} ${symbol}\n   📍 ${wallet.address}`
        })
        .join("\n\n")

      await this.updateKeyboard(ctx, "wallets_list", keyboard, {
        walletList,
        totalWallets: wallets.length,
        currentPage: currentPage + 1,
        totalPages
      })
    } catch (error) {
      log.error(`Error in showWalletListPage: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show create wallet form
   * @param ctx Callback query context
   */
  private async showCreateWalletForm(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard()

    // Add chain selection buttons
    const chains = dataChainName.slice(0, 6) // Show first 6 chains
    for (let i = 0; i < chains.length; i += 2) {
      const row = new InlineKeyboard()
      row.text(`🔗 ${formatChainName(chains[i])}`, `create_wallet:${chains[i]}`)
      if (chains[i + 1]) {
        row.text(`🔗 ${formatChainName(chains[i + 1])}`, `create_wallet:${chains[i + 1]}`)
      }
      keyboard.row().add(row)
    }

    keyboard.row().text("🔙 Back to Menu", "back:main_menu")

    await this.updateKeyboard(ctx, "createwallet_usage", keyboard)
  }

  /**
   * Show import wallet form
   * @param ctx Callback query context
   */
  private async showImportWalletForm(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("📝 Enter Details Manually", "import_manual").text("📋 Paste Seed Phrase", "import_seed").row().text("🔙 Back to Menu", "back:main_menu")

    await this.updateKeyboard(ctx, "importwallet_usage", keyboard)
  }

  /**
   * Show user statistics
   * @param ctx Callback query context
   */
  private async showUserStats(ctx: any): Promise<void> {
    try {
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.answerCallback(ctx, "callback_user_not_found")
        await this.deleteKeyboard(ctx)
        return
      }

      const wallets = await Wallet.getAllForUser(user.id)
      const totalWallets = wallets.length
      const totalBalance = wallets.reduce((sum, wallet) => sum + wallet.balance, BigInt(0))

      const keyboard = new InlineKeyboard().text("💼 View Wallets", "wallet_action:list").text("📊 Detailed Stats", "stats_detailed").row().text("🔙 Back to Menu", "back:main_menu")

      await this.updateKeyboard(ctx, "stats_display", keyboard, {
        totalWallets,
        totalBalance: totalBalance.toString(),
        username: user.username || "Unknown",
        role: user.role
      })
    } catch (error) {
      log.error(`Error in showUserStats: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show settings menu
   * @param ctx Callback query context
   */
  private async showSettings(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("💰 Fee Settings", "settings:fees").text("🔗 Chain Settings", "settings:chains").row().text("🌐 Language", "settings:language").text("🔔 Notifications", "settings:notifications").row().text("🔙 Back to Menu", "back:main_menu")

    await this.updateKeyboard(ctx, "settings_menu", keyboard)
  }

  /**
   * Show help menu
   * @param ctx Callback query context
   */
  private async showHelpMenu(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("💼 Wallet Commands", "help:wallets").text("📊 Trading Commands", "help:trading").row().text("⚙️ Config Commands", "help:config").text("🔙 Back to Menu", "back:main_menu")

    await this.updateKeyboard(ctx, "help", keyboard)
  }

  /**
   * Handle wallet detail actions
   * @param ctx Callback query context
   * @param params Detail parameters
   */
  private async handleWalletDetail(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    const walletIdStr = params[1]

    if (!walletIdStr) {
      await this.deleteKeyboard(ctx)
      return
    }

    const walletId = parseInt(walletIdStr)

    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    const wallet = await Wallet.getById(walletId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_not_found")
      await this.deleteKeyboard(ctx)
      return
    }

    switch (action) {
      case "balance":
        await this.showWalletBalance(ctx, wallet)
        break
      case "history":
        await this.showWalletHistory(ctx, wallet)
        break
      case "stats":
        await this.showWalletStats(ctx, wallet)
        break
      case "delete":
        await this.showDeleteConfirmation(ctx, wallet)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle wallet delete selection
   * @param ctx Callback query context
   * @param params Selection parameters
   */
  private async handleWalletDeleteSelect(ctx: any, params: string[]): Promise<void> {
    const walletId = parseInt(params[0])
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    const wallet = await Wallet.getById(walletId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_not_found")
      await this.deleteKeyboard(ctx)
      return
    }

    await this.showDeleteConfirmation(ctx, wallet)
  }

  /**
   * Handle chain detail actions
   * @param ctx Callback query context
   * @param params Detail parameters
   */
  private async handleChainDetail(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    const chain = params[1] as TypeChainName

    switch (action) {
      case "fees":
        await this.showChainFees(ctx, chain)
        break
      case "stats":
        await this.showChainStats(ctx, chain)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle create wallet chain selection
   * @param ctx Callback query context
   * @param params Chain parameters
   */
  private async handleCreateWalletChain(ctx: any, params: string[]): Promise<void> {
    const chain = params[0] as TypeChainName

    const keyboard = new InlineKeyboard().text("📝 Enter Wallet Name", `create_name:${chain}`).text("🎲 Auto Generate", `create_auto:${chain}`).row().text("🔙 Back to Chains", "wallet_action:create")

    await this.updateKeyboard(ctx, "createwallet_chain_selected", keyboard, {
      chainName: formatChainName(chain),
      symbol: getCurrencySymbol(chain)
    })
  }

  /**
   * Handle manual import
   * @param ctx Callback query context
   */
  private async handleImportManual(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("🔙 Back to Import Options", "wallet_action:import").text("❌ Cancel", "cancel")

    await this.updateKeyboard(ctx, "import_manual_instructions", keyboard)
  }

  /**
   * Handle seed import
   * @param ctx Callback query context
   */
  private async handleImportSeed(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("🔙 Back to Import Options", "wallet_action:import").text("❌ Cancel", "cancel")

    await this.updateKeyboard(ctx, "import_seed_instructions", keyboard)
  }

  /**
   * Handle help section selection
   * @param ctx Callback query context
   * @param params Help parameters
   */
  private async handleHelpSection(ctx: any, params: string[]): Promise<void> {
    const section = params[0]

    const keyboard = new InlineKeyboard().text("🔙 Back to Help", "wallet_action:help").text("🏠 Main Menu", "back:main_menu")

    switch (section) {
      case "wallets":
        await this.updateKeyboard(ctx, "help_wallets", keyboard)
        break
      case "trading":
        await this.updateKeyboard(ctx, "help_trading", keyboard)
        break
      case "config":
        await this.updateKeyboard(ctx, "help_config", keyboard)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle settings actions
   * @param ctx Callback query context
   * @param params Settings parameters
   */
  private async handleSettingsAction(ctx: any, params: string[]): Promise<void> {
    const action = params[0]

    const keyboard = new InlineKeyboard().text("🔙 Back to Settings", "wallet_action:settings").text("🏠 Main Menu", "back:main_menu")

    switch (action) {
      case "fees":
        await this.updateKeyboard(ctx, "settings_fees", keyboard)
        break
      case "chains":
        await this.updateKeyboard(ctx, "settings_chains", keyboard)
        break
      case "language":
        await this.updateKeyboard(ctx, "settings_language", keyboard)
        break
      case "notifications":
        await this.updateKeyboard(ctx, "settings_notifications", keyboard)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle detailed stats
   * @param ctx Callback query context
   */
  private async handleDetailedStats(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("💼 View Wallets", "wallet_action:list").text("🔙 Back to Stats", "wallet_action:stats").row().text("🏠 Main Menu", "back:main_menu")

    await this.updateKeyboard(ctx, "stats_detailed", keyboard)
  }

  /**
   * Show wallet balance details
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showWalletBalance(ctx: any, wallet: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("📊 View History", `wallet_detail:history:${wallet.id}`).text("📈 View Stats", `wallet_detail:stats:${wallet.id}`).row().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)

    const symbol = getCurrencySymbol(wallet.chain as TypeChainName)
    const balance = wallet.balance.toString()
    const chainName = formatChainName(wallet.chain as TypeChainName)

    await this.updateKeyboard(ctx, "balance_display", keyboard, {
      walletName: wallet.name,
      balance,
      symbol,
      chainName
    })
  }

  /**
   * Show wallet trading history
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showWalletHistory(ctx: any, wallet: any): Promise<void> {
    try {
      const history = await TradingHistory.getForWallet(BigInt(wallet.id), 10)

      const keyboard = new InlineKeyboard().text("💰 View Balance", `wallet_detail:balance:${wallet.id}`).text("📈 View Stats", `wallet_detail:stats:${wallet.id}`).row().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)

      if (history.length === 0) {
        await this.updateKeyboard(ctx, "history_empty", keyboard, { walletName: wallet.name })
        return
      }

      const historyText = history
        .map((trade, index) => {
          const operation = trade.operation === "B" ? "🟢 BUY" : "🔴 SELL"
          const status = trade.success ? "✅" : "❌"
          const amount = trade.amount.toString()
          const symbol = getCurrencySymbol(trade.chain as TypeChainName)
          const date = trade.createdAt?.toLocaleDateString() || "Unknown"

          return `${index + 1}. ${operation} ${status}\n   💰 ${amount} ${symbol}\n   📅 ${date}`
        })
        .join("\n\n")

      await this.updateKeyboard(ctx, "history_display", keyboard, {
        walletName: wallet.name,
        historyText
      })
    } catch (error) {
      log.error(`Error in showWalletHistory: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show wallet statistics
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showWalletStats(ctx: any, wallet: any): Promise<void> {
    try {
      const stats = await TradingHistory.getWalletStats(BigInt(wallet.id))

      const keyboard = new InlineKeyboard().text("💰 View Balance", `wallet_detail:balance:${wallet.id}`).text("📊 View History", `wallet_detail:history:${wallet.id}`).row().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)

      if (!stats) {
        await this.updateKeyboard(ctx, "stats_empty", keyboard, { walletName: wallet.name })
        return
      }

      const successRate = stats.totalTrades > 0 ? ((stats.successTrades / stats.totalTrades) * 100).toFixed(2) : "0"
      const symbol = getCurrencySymbol(wallet.chain as TypeChainName)

      await this.updateKeyboard(ctx, "stats_display", keyboard, {
        walletName: wallet.name,
        totalTrades: stats.totalTrades,
        successTrades: stats.successTrades,
        failedTrades: stats.failedTrades,
        successRate,
        totalVolume: stats.totalVolume.toString(),
        successVolume: stats.successVolume.toString(),
        symbol,
        buyTrades: stats.buyTrades,
        sellTrades: stats.sellTrades
      })
    } catch (error) {
      log.error(`Error in showWalletStats: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show delete confirmation dialog
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showDeleteConfirmation(ctx: any, wallet: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("✅ Yes, Delete", `confirm_delete:${wallet.id}`).text("❌ Cancel", "cancel").row().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)

    await this.updateKeyboard(ctx, "deletewallet_confirm", keyboard, {
      walletName: wallet.name,
      chainName: formatChainName(wallet.chain as TypeChainName),
      address: wallet.address
    })
  }

  /**
   * Show chain fees
   * @param ctx Callback query context
   * @param chain Chain name
   */
  private async showChainFees(ctx: any, chain: TypeChainName): Promise<void> {
    try {
      const fees = await Fees.getByChain(chain)

      const keyboard = new InlineKeyboard().text("📊 Network Stats", `chain_detail:stats:${chain}`).text("🔙 Back to Chain", `chain_select:${chain}:info`)

      if (fees.length === 0) {
        await this.updateKeyboard(ctx, "fees_chain_empty", keyboard, {
          chainName: formatChainName(chain)
        })
        return
      }

      const chainName = formatChainName(chain)
      const feesList = fees
        .map((fee, index) => {
          return `${index + 1}. Chain ID: ${fee.chainId}\n   💰 Fee: ${fee.fee}%\n   📍 Receiver: \`${fee.receiver}\``
        })
        .join("\n\n")

      await this.updateKeyboard(ctx, "fees_chain_display", keyboard, {
        chainName,
        feesList
      })
    } catch (error) {
      log.error(`Error in showChainFees: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show chain statistics
   * @param ctx Callback query context
   * @param chain Chain name
   */
  private async showChainStats(ctx: any, chain: TypeChainName): Promise<void> {
    const keyboard = new InlineKeyboard().text("💰 View Fees", `chain_detail:fees:${chain}`).text("🔙 Back to Chain", `chain_select:${chain}:info`)

    // For now, show basic chain info as stats
    await this.updateKeyboard(ctx, "chain_info", keyboard, {
      chainName: formatChainName(chain),
      symbol: getCurrencySymbol(chain),
      chainId: getChainId(chain),
      chain
    })
  }
}
