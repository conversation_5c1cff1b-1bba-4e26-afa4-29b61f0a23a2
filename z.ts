import { drizzle } from "drizzle-orm/postgres-js"
import postgres from "postgres"

// Disable prefetch as it is not supported for "Transaction" pool mode
const strPooler = process.env.DATABASE_URI || ``
const client = postgres(strPooler, { prepare: false })
const db = drizzle(client)
const run = async () => {
  try {
    const res = await db.execute(`SELECT name FROM pg_timezone_names`)
    console.log({ res })
  } catch (err) {
    console.log(err)
  }
}

run()
