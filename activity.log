[2025-06-20 06:51:15:919] [ERROR] Failed to get user by ID: PostgresError: Tenant or user not found
[2025-06-20 06:51:36:445] [ERROR] Failed to update keyboard, attempting deletion: GrammyError: Call to 'editMessageText' failed! (400: Bad Request: can't parse entities: Character '.' is reserved and must be escaped with the preceding '\')
[2025-06-20 06:52:14:327] [ERROR] Failed to update keyboard, attempting deletion: GrammyError: Call to 'editMessageText' failed! (400: Bad Request: can't parse entities: Character '.' is reserved and must be escaped with the preceding '\')
