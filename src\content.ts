import { watch } from "chokidar"
import { basename, join } from "node:path"
import { readdirSync, readFileSync } from "node:fs"
import { readFile, writeFile } from "node:fs/promises"
import { render } from "micromustache"

export class Content {
  public stateContent = new Map<string, string>()
  constructor(public filterExtensionFile: string = `md`, public pathContent: string = join(process.cwd(), `content`)) {
    // watch for changes in the content folder (example only md files) and store to state mapping (stateContent) on change
    watch(this.pathContent, {
      ignored: (path, stats: any) => stats?.isFile() && !path.endsWith(`.${this.filterExtensionFile}`), // only watch {fileExt} files
      persistent: true
    }).on(`change`, async (path) => {
      const fullFileName = basename(path)
      const pathFile = join(this.pathContent, fullFileName)
      const content = await readFile(pathFile, { encoding: `utf8` })
      const escapedContent = this.escapeMarkdownV2(content)
      this.stateContent.set(fullFileName, escapedContent)
    })

    // read all files and store to state mapping (stateContent) on start
    const arrFn = readdirSync(this.pathContent, { encoding: `utf8` })
    for (let index = 0; index < arrFn.length; index++) {
      const fullFileName = arrFn[index]
      if (fullFileName && fullFileName.endsWith(`.${this.filterExtensionFile}`) === true) {
        const pathFile = join(this.pathContent, fullFileName)
        const content = readFileSync(pathFile, { encoding: `utf8` })
        const escapedContent = this.escapeMarkdownV2(content)
        this.stateContent.set(fullFileName, escapedContent)
      }
    }
  }

  /**
   * Escapes special characters for Telegram's MarkdownV2 format
   * @param text The text to escape
   * @returns The escaped text
   */
  private escapeMarkdownV2(text: string): string {
    const specialChars = ["_", "*", "[", "]", "(", ")", "~", "`", ">", "#", "+", "-", "=", "|", "{", "}", ".", "!"]
    let escapedText = text
    for (const char of specialChars) {
      escapedText = escapedText.replace(new RegExp(`\\${char}`, "g"), `\\${char}`)
    }
    return escapedText
  }

  /**
   * Get content from state mapping (stateContent)
   * @param fileName The file name to get content from
   * @param data Optional data to replace variables in the content
   * @returns The content with variables replaced
   */
  public get(fileName: string, data?: Record<string, any>) {
    const content = this.stateContent.get(`${fileName}.${this.filterExtensionFile}`) || ``
    if (data) {
      return render(content, data)
    }
    return content
  }

  /**
   * Set content in state mapping (stateContent)
   * @param fileName The file name to set content for
   * @param content The content to set
   */
  public set(fileName: string, content: string) {
    const escapedContent = this.escapeMarkdownV2(content)
    this.stateContent.set(`${fileName}.${this.filterExtensionFile}`, escapedContent)
  }

  /**
   * Write content to file and set in state mapping (stateContent)
   * @param fileName The file name to write content to
   * @param content The content to write
   * @returns A promise that resolves when the file is written
   */
  public write(fileName: string, content: string) {
    this.set(fileName, content)
    const fullFileName = `${fileName}.${this.filterExtensionFile}`
    const pathFile = join(this.pathContent, fullFileName)
    return writeFile(pathFile, content, { encoding: `utf8` })
  }
}
